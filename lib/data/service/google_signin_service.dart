import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart';

class GoogleSignInService {
  static final GoogleSignInService _instance = GoogleSignInService._internal();
  factory GoogleSignInService() => _instance;
  GoogleSignInService._internal();

  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  /// Step 1-2: Handle Google Sign-In and Firebase Auth, return UserCredential
  Future<UserCredential?> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('Starting Google Sign-In process...');
      }

      // Check if Firebase is initialized
      if (_firebaseAuth.app.options.projectId.isEmpty) {
        if (kDebugMode) {
          print('Firebase not configured properly. Please add real google-services.json');
        }
        throw Exception('Firebase not configured. Please set up Firebase Console and add google-services.json');
      }

      // Step 1: Trigger the Google Sign-In flow
      if (kDebugMode) {
        print('Triggering Google Sign-In flow...');
      }
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        if (kDebugMode) {
          print('User cancelled Google Sign-In');
        }
        return null;
      }

      if (kDebugMode) {
        print('Google user obtained: ${googleUser.email}');
      }

      // Step 2: Obtain the auth details from the request
      if (kDebugMode) {
        print('Getting authentication details...');
      }
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens');
      }

      if (kDebugMode) {
        print('Google auth tokens obtained');
      }

      // Step 3: Create a new credential for Firebase Auth
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      if (kDebugMode) {
        print('Firebase credential created');
      }

      // Step 4: Sign in to Firebase with the Google credential
      if (kDebugMode) {
        print('Signing in to Firebase...');
      }
      final UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user == null) {
        throw Exception('Failed to sign in to Firebase');
      }

      if (kDebugMode) {
        print('Google Sign-In successful');
        print('User: ${userCredential.user?.displayName}');
        print('Email: ${userCredential.user?.email}');
        print('User ID: ${userCredential.user?.uid}');
      }

      return userCredential;
    } catch (e) {
      if (kDebugMode) {
        print('Google Sign-In error: $e');
        print('Error type: ${e.runtimeType}');
        if (e.toString().contains('ApiException: 10')) {
          print('This is a DEVELOPER_ERROR - check SHA-1 fingerprint and package name in Firebase Console');
        }
      }
      rethrow; // Re-throw the error so the calling code can handle it
    }
  }

  /// Sign out from both Google and Firebase
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _firebaseAuth.signOut();
    } catch (e) {
      if (kDebugMode) {
        print('Sign out error: $e');
      }
    }
  }

  /// Get current user info (if signed in)
  User? get currentUser => _firebaseAuth.currentUser;

  /// Check if user is currently signed in
  bool get isSignedIn => _firebaseAuth.currentUser != null;
}
